{"os": "Linux-6.5.0-34-generic-x86_64-with-glibc2.35", "python": "CPython 3.9.21", "startedAt": "2025-06-06T07:19:12.011172Z", "args": ["--hook-layer", "19", "--wandb-run-name", "llama_3_1_8b_layer19_gated_sae"], "program": "/data_x/junkim100/projects/scheming_sae/itas/sae_train.py", "codePath": "sae_train.py", "git": {"remote": "https://github.com/junkim100/ITAS.git", "commit": "de3daa818f06bfb9494814f9c9a53ce3ed64a54e"}, "email": "<EMAIL>", "root": "/data_x/junkim100/projects/scheming_sae/itas", "host": "nlp-server-16", "executable": "/mnt/raid6/junkim100/miniconda3/envs/itas/bin/python", "codePathLocal": "sae_train.py", "cpu_count": 64, "cpu_count_logical": 128, "gpu": "NVIDIA RTX A6000", "gpu_count": 8, "disk": {"/": {"total": "1964618686464", "used": "185385816064"}}, "memory": {"total": "2151664984064"}, "cpu": {"count": 64, "countLogical": 128}, "gpu_nvidia": [{"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere", "uuid": "GPU-855eb063-38d7-c437-10c6-9868f029f701"}, {"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere", "uuid": "GPU-d6dc840b-cb82-39f6-5ea2-b0a4f18bdf6e"}, {"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere", "uuid": "GPU-47d47859-f7c9-3a5d-041f-91caee50ac0c"}, {"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere", "uuid": "GPU-17d9dba6-e295-fcfa-0378-5fb370c94055"}, {"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere", "uuid": "GPU-13aaa27d-9c08-63b9-a5f4-3459c3f0ffbe"}, {"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere", "uuid": "GPU-8c83a879-8638-6ecc-eac2-281d5b59d505"}, {"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere", "uuid": "GPU-3f97dcea-347c-cd62-b3b8-52705086bf09"}, {"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere", "uuid": "GPU-e37570d8-ee58-0645-2da3-3234a6b9ef3f"}], "cudaVersion": "12.3"}