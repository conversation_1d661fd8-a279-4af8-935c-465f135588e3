"""
Representation Engineer for SAE-based model interventions.

This module provides tools for engineering model representations using
SAE features to achieve targeted behavioral modifications.
"""

import logging
from typing import Dict, Any, List, Optional, Callable, Tuple
import torch
import torch.nn as nn
from torch import Tensor
import numpy as np
from dataclasses import dataclass
from transformers import PreTrainedModel, PreTrainedTokenizer

from ..core.sae import SAE
from .function_extractor import FunctionExtractor, FunctionExtractionResult

logger = logging.getLogger(__name__)


@dataclass
class InterventionResult:
    """Result from a representation intervention."""

    original_output: Any
    """Original model output before intervention"""

    modified_output: Any
    """Modified model output after intervention"""

    intervention_strength: float
    """Strength of the applied intervention"""

    affected_features: List[int]
    """List of SAE features affected by intervention"""

    metadata: Dict[str, Any]
    """Additional metadata about the intervention"""

    def __iter__(self):
        """
        Make InterventionResult iterable for backward compatibility.

        Iterates over the original_output, which contains the list of results.
        This allows users to do: for result in intervention_result
        """
        if isinstance(self.original_output, list):
            return iter(self.original_output)
        else:
            # If original_output is not a list, return a single-item iterator
            return iter([self.original_output])

    def __len__(self):
        """Return the number of results."""
        if isinstance(self.original_output, list):
            return len(self.original_output)
        else:
            return 1

    def __getitem__(self, index):
        """Allow indexing into the results."""
        if isinstance(self.original_output, list):
            return self.original_output[index]
        else:
            if index == 0:
                return self.original_output
            else:
                raise IndexError("Index out of range")

    def is_activation_comparison(self):
        """Check if this result is from activation comparison rather than generation."""
        return (
            "method" in self.metadata
            and self.metadata["method"] == "activation_comparison"
        )

    def get_original_outputs(self):
        """Get the original outputs separately."""
        if "original_outputs" in self.metadata:
            return self.metadata["original_outputs"]
        return None

    def get_modified_outputs(self):
        """Get the modified outputs separately."""
        return self.modified_output

    def get_combined_results(self):
        """Get the combined results for backward compatibility."""
        return self.original_output


class RepresentationEngineer:
    """
    Engineer model representations using SAE features.

    Provides tools for targeted model interventions through SAE feature
    manipulation to achieve specific behavioral modifications.
    """

    def __init__(
        self,
        model: PreTrainedModel,
        tokenizer: PreTrainedTokenizer,
        sae: SAE,
        hook_layer: int,
        hook_name: str,
    ):
        """
        Initialize representation engineer.

        Args:
            model: Target model for interventions
            tokenizer: Tokenizer for the model
            sae: Trained SAE for feature extraction
            hook_layer: Layer to apply interventions
            hook_name: Name of the hook point
        """
        self.model = model
        self.tokenizer = tokenizer
        self.sae = sae
        self.hook_layer = hook_layer
        self.hook_name = hook_name

        # Intervention state
        self.active_hooks = []
        self.intervention_functions = {}

        # Check generation capability and warn if not available
        if not hasattr(self.model, "generate"):
            logger.info(
                "Model does not have generation capability. "
                "Text generation features will fall back to activation comparison. "
                "To enable text generation, load the model with load_for_generation=True "
                "or use force_generation=True in test_intervention()."
            )

    @classmethod
    def from_model_name(
        cls,
        model_name: str,
        sae: SAE,
        hook_layer: int,
        hook_name: str,
        load_for_generation: bool = True,
        **model_kwargs,
    ):
        """
        Create RepresentationEngineer by loading model from name.

        Args:
            model_name: HuggingFace model name
            sae: Trained SAE for feature extraction
            hook_layer: Layer to apply interventions
            hook_name: Name of the hook point
            load_for_generation: Whether to load model with generation capability
            **model_kwargs: Additional arguments for model loading

        Returns:
            RepresentationEngineer instance
        """
        from ..utils import load_model_and_tokenizer

        model, tokenizer = load_model_and_tokenizer(
            model_name=model_name,
            load_for_generation=load_for_generation,
            **model_kwargs,
        )

        return cls(
            model=model,
            tokenizer=tokenizer,
            sae=sae,
            hook_layer=hook_layer,
            hook_name=hook_name,
        )

    def create_steering_vector(
        self,
        positive_examples: List[str],
        negative_examples: List[str],
        method: str = "difference",
    ) -> Tensor:
        """
        Create a steering vector from positive and negative examples.

        Args:
            positive_examples: Examples representing desired behavior
            negative_examples: Examples representing undesired behavior
            method: Method for creating steering vector

        Returns:
            Steering vector in activation space
        """
        logger.info(
            f"Creating steering vector from {len(positive_examples)} positive and {len(negative_examples)} negative examples"
        )

        # Get activations for examples
        pos_activations = self._get_activations_for_texts(positive_examples)
        neg_activations = self._get_activations_for_texts(negative_examples)

        if method == "difference":
            # Simple difference method
            pos_mean = pos_activations.mean(dim=0)
            neg_mean = neg_activations.mean(dim=0)
            steering_vector = pos_mean - neg_mean

        elif method == "pca":
            # PCA-based method
            all_activations = torch.cat([pos_activations, neg_activations], dim=0)
            labels = torch.cat(
                [torch.ones(len(pos_activations)), torch.zeros(len(neg_activations))]
            )

            # Compute PCA direction
            centered = all_activations - all_activations.mean(dim=0)
            U, S, V = torch.pca_lowrank(centered, q=1)
            steering_vector = V[:, 0]

            # Ensure direction points toward positive examples
            pos_proj = (pos_activations @ steering_vector).mean()
            neg_proj = (neg_activations @ steering_vector).mean()
            if pos_proj < neg_proj:
                steering_vector = -steering_vector

        else:
            raise ValueError(f"Unknown steering method: {method}")

        logger.info(
            f"Created steering vector with norm: {steering_vector.norm().item():.4f}"
        )
        return steering_vector

    def _get_activations_for_texts(self, texts: List[str]) -> Tensor:
        """Get model activations for a list of texts."""
        all_activations = []

        with torch.no_grad():
            for text in texts:
                # Tokenize
                inputs = self.tokenizer(text, return_tensors="pt", truncation=True)
                input_ids = inputs["input_ids"].to(self.model.device)

                # Get activations using hook
                activations = self._extract_activations(input_ids)

                # Use last token activation (or mean)
                if len(activations.shape) == 3:  # [batch, seq, hidden]
                    activation = activations[0, -1, :]  # Last token
                else:
                    activation = activations[0]  # Already 2D

                all_activations.append(activation)

        return torch.stack(all_activations)

    def _extract_activations(self, input_ids: Tensor) -> Tensor:
        """Extract activations from model at specified layer."""
        activations = []

        def hook_fn(module, input, output):
            if isinstance(output, tuple):
                activations.append(output[0].detach())
            else:
                activations.append(output.detach())

        # Register hook
        target_module = self.model
        for part in self.hook_name.split("."):
            target_module = getattr(target_module, part)

        hook_handle = target_module.register_forward_hook(hook_fn)

        try:
            # Forward pass
            with torch.no_grad():
                _ = self.model(input_ids)

            return activations[0] if activations else None
        finally:
            hook_handle.remove()

    def apply_steering_intervention(
        self,
        steering_vector: Tensor,
        strength: float = 1.0,
        method: str = "add",
    ) -> Callable:
        """
        Create intervention function for steering.

        Args:
            steering_vector: Vector to steer toward
            strength: Strength of intervention
            method: Method for applying intervention

        Returns:
            Intervention function
        """

        def intervention_fn(module, input, output):
            if isinstance(output, tuple):
                hidden_states = output[0]
                other_outputs = output[1:]
            else:
                hidden_states = output
                other_outputs = ()

            # Apply intervention
            if method == "add":
                # Add steering vector
                modified = hidden_states + strength * steering_vector.unsqueeze(
                    0
                ).unsqueeze(0)
            elif method == "project":
                # Project onto steering direction
                projection = (hidden_states @ steering_vector) / steering_vector.norm()
                modified = (
                    hidden_states
                    + strength * projection.unsqueeze(-1) * steering_vector
                )
            else:
                raise ValueError(f"Unknown intervention method: {method}")

            if other_outputs:
                return (modified,) + other_outputs
            else:
                return modified

        return intervention_fn

    def apply_feature_intervention(
        self,
        feature_indices: List[int],
        intervention_values: List[float],
        method: str = "set",
    ) -> Callable:
        """
        Create intervention function for specific SAE features.

        Args:
            feature_indices: Indices of features to intervene on
            intervention_values: Values to set/add for each feature
            method: Method for intervention (set, add, multiply)

        Returns:
            Intervention function
        """

        def intervention_fn(module, input, output):
            if isinstance(output, tuple):
                hidden_states = output[0]
                other_outputs = output[1:]
            else:
                hidden_states = output
                other_outputs = ()

            # Encode through SAE
            sae_features = self.sae.encode(hidden_states)

            # Apply feature interventions
            for feat_idx, value in zip(feature_indices, intervention_values):
                if method == "set":
                    sae_features[:, :, feat_idx] = value
                elif method == "add":
                    sae_features[:, :, feat_idx] += value
                elif method == "multiply":
                    sae_features[:, :, feat_idx] *= value
                else:
                    raise ValueError(f"Unknown intervention method: {method}")

            # Decode back to activation space
            modified = self.sae.decode(sae_features)

            if other_outputs:
                return (modified,) + other_outputs
            else:
                return modified

        return intervention_fn

    def register_intervention(
        self,
        intervention_fn: Callable,
        intervention_name: str,
    ) -> None:
        """
        Register an intervention function.

        Args:
            intervention_fn: Function to apply intervention
            intervention_name: Name for the intervention
        """
        # Get target module
        target_module = self.model
        for part in self.hook_name.split("."):
            target_module = getattr(target_module, part)

        # Register hook
        hook_handle = target_module.register_forward_hook(intervention_fn)

        # Store for later removal
        self.active_hooks.append(hook_handle)
        self.intervention_functions[intervention_name] = intervention_fn

        logger.info(f"Registered intervention: {intervention_name}")

    def remove_intervention(self, intervention_name: str) -> None:
        """
        Remove a specific intervention.

        Args:
            intervention_name: Name of intervention to remove
        """
        if intervention_name in self.intervention_functions:
            del self.intervention_functions[intervention_name]
            logger.info(f"Removed intervention: {intervention_name}")
        else:
            logger.warning(f"Intervention not found: {intervention_name}")

    def remove_all_interventions(self) -> None:
        """Remove all active interventions."""
        for hook_handle in self.active_hooks:
            hook_handle.remove()

        self.active_hooks.clear()
        self.intervention_functions.clear()

        logger.info("Removed all interventions")

    def _try_add_generation_capability(self) -> bool:
        """
        Try to add generation capability to a model that doesn't have it.

        This attempts to add the generate method from AutoModelForCausalLM
        to models loaded with AutoModel.

        Returns:
            True if generation capability was successfully added, False otherwise
        """
        try:
            from transformers import AutoModelForCausalLM
            from transformers.generation.utils import GenerationMixin

            # Check if the model is already a causal LM
            if hasattr(self.model, "generate"):
                return True

            # Check if the model has the necessary components for generation
            if not hasattr(self.model, "config") or not hasattr(
                self.model.config, "vocab_size"
            ):
                logger.warning(
                    "Model does not have the necessary configuration for generation"
                )
                return False

            # Try to add generation methods
            if not isinstance(self.model, GenerationMixin):
                # Add GenerationMixin methods to the model
                for attr_name in dir(GenerationMixin):
                    if not attr_name.startswith("_") and callable(
                        getattr(GenerationMixin, attr_name)
                    ):
                        if not hasattr(self.model, attr_name):
                            setattr(
                                self.model,
                                attr_name,
                                getattr(GenerationMixin, attr_name).__get__(self.model),
                            )

            # Verify that generate method is now available
            if hasattr(self.model, "generate"):
                logger.info("Successfully added generation capability to the model")
                return True
            else:
                logger.warning("Failed to add generation capability")
                return False

        except Exception as e:
            logger.warning(f"Error while trying to add generation capability: {e}")
            return False

    def test_intervention(
        self,
        test_texts: List[str],
        intervention_fn: Callable,
        generation_kwargs: Optional[Dict[str, Any]] = None,
        force_generation: bool = False,
    ) -> InterventionResult:
        """
        Test an intervention on given texts.

        Args:
            test_texts: Texts to test intervention on
            intervention_fn: Intervention function to apply
            generation_kwargs: Arguments for text generation
            force_generation: If True, attempt to add generation capability to the model

        Returns:
            Intervention test results
        """
        # Check if model supports generation
        if not hasattr(self.model, "generate"):
            if force_generation:
                logger.info("Attempting to add generation capability to the model...")
                success = self._try_add_generation_capability()
                if not success:
                    logger.warning(
                        "Could not add generation capability. "
                        "To use generation features, load the model with load_for_generation=True. "
                        "Falling back to activation comparison."
                    )
                    return self._test_intervention_activations(
                        intervention_fn, test_texts
                    )
            else:
                logger.warning(
                    "Model does not have 'generate' method. "
                    "To use generation features, load the model with load_for_generation=True "
                    "or set force_generation=True to attempt adding generation capability. "
                    "Falling back to activation comparison."
                )
                return self._test_intervention_activations(intervention_fn, test_texts)

        if generation_kwargs is None:
            generation_kwargs = {"max_new_tokens": 20, "do_sample": False}

        results = []
        original_outputs = []
        modified_outputs = []

        for text in test_texts:
            # Get original output
            inputs = self.tokenizer(text, return_tensors="pt")
            input_ids = inputs["input_ids"].to(self.model.device)

            with torch.no_grad():
                original_output = self.model.generate(input_ids, **generation_kwargs)

            # Apply intervention and get modified output
            self.register_intervention(intervention_fn, "test_intervention")

            try:
                with torch.no_grad():
                    modified_output = self.model.generate(
                        input_ids, **generation_kwargs
                    )
            finally:
                self.remove_intervention("test_intervention")

            # Decode outputs
            original_text = self.tokenizer.decode(
                original_output[0], skip_special_tokens=True
            )
            modified_text = self.tokenizer.decode(
                modified_output[0], skip_special_tokens=True
            )

            # Store individual results for backward compatibility
            result_dict = {
                "input": text,
                "original": original_text,
                "modified": modified_text,
            }
            results.append(result_dict)

            # Store separate original and modified outputs
            original_outputs.append(
                {
                    "input": text,
                    "output": original_text,
                    "tokens": original_output[0],
                }
            )
            modified_outputs.append(
                {
                    "input": text,
                    "output": modified_text,
                    "tokens": modified_output[0],
                }
            )

        return InterventionResult(
            original_output=results,  # Combined results for backward compatibility
            modified_output=modified_outputs,  # Separate modified outputs
            intervention_strength=1.0,  # Default
            affected_features=[],  # Would need to track this
            metadata={
                "test_texts": test_texts,
                "generation_kwargs": generation_kwargs,
                "original_outputs": original_outputs,  # Separate original outputs
            },
        )

    def _test_intervention_activations(
        self,
        intervention_fn: Callable,
        test_texts: List[str],
    ) -> InterventionResult:
        """
        Test intervention by comparing activations instead of generation.

        This is a fallback method for models that don't support generation.

        Args:
            intervention_fn: Intervention function to test
            test_texts: List of test input texts

        Returns:
            Intervention test results with activation comparisons
        """
        results = []
        original_outputs = []
        modified_outputs = []

        for text in test_texts:
            # Get original activations
            inputs = self.tokenizer(text, return_tensors="pt")
            input_ids = inputs["input_ids"].to(self.model.device)

            # Get original activations
            original_activations = self._extract_activations(input_ids)

            # Apply intervention and get modified activations
            self.register_intervention(intervention_fn, "test_intervention")

            try:
                modified_activations = self._extract_activations(input_ids)
            finally:
                self.remove_intervention("test_intervention")

            # Compute activation difference metrics
            if original_activations is not None and modified_activations is not None:
                diff_norm = torch.norm(
                    modified_activations - original_activations
                ).item()
                cosine_sim = torch.nn.functional.cosine_similarity(
                    original_activations.flatten(),
                    modified_activations.flatten(),
                    dim=0,
                ).item()
                orig_norm = torch.norm(original_activations).item()
                mod_norm = torch.norm(modified_activations).item()
            else:
                diff_norm = 0.0
                cosine_sim = 1.0
                orig_norm = 0.0
                mod_norm = 0.0

            # Store individual results for backward compatibility
            result_dict = {
                "input": text,
                "original": f"Original activations (norm: {orig_norm:.4f})",
                "modified": f"Modified activations (norm: {mod_norm:.4f})",
                "activation_diff_norm": diff_norm,
                "cosine_similarity": cosine_sim,
            }
            results.append(result_dict)

            # Store separate original and modified outputs
            original_outputs.append(
                {
                    "input": text,
                    "output": f"Original activations (norm: {orig_norm:.4f})",
                    "activations": original_activations,
                    "norm": orig_norm,
                }
            )
            modified_outputs.append(
                {
                    "input": text,
                    "output": f"Modified activations (norm: {mod_norm:.4f})",
                    "activations": modified_activations,
                    "norm": mod_norm,
                    "activation_diff_norm": diff_norm,
                    "cosine_similarity": cosine_sim,
                }
            )

        return InterventionResult(
            original_output=results,  # Combined results for backward compatibility
            modified_output=modified_outputs,  # Separate modified outputs
            intervention_strength=1.0,  # Default
            affected_features=[],  # Would need to track this
            metadata={
                "test_texts": test_texts,
                "method": "activation_comparison",
                "note": "Model does not support generation - using activation comparison",
                "original_outputs": original_outputs,  # Separate original outputs
            },
        )

    def extract_behavioral_function(
        self,
        behavior_examples: Dict[str, List[str]],
        extraction_method: str = "contrastive",
    ) -> FunctionExtractionResult:
        """
        Extract a behavioral function from examples.

        Args:
            behavior_examples: Dictionary with 'positive' and 'negative' example lists
            extraction_method: Method for extracting the function

        Returns:
            Function extraction result
        """
        logger.info("Extracting behavioral function from examples")

        # Get activations for positive and negative examples
        pos_activations = self._get_activations_for_texts(behavior_examples["positive"])
        neg_activations = self._get_activations_for_texts(behavior_examples["negative"])

        # Create function extractor
        function_extractor = FunctionExtractor(self.sae)

        if extraction_method == "contrastive":
            # Use positive examples as target, negative as context
            result = function_extractor.extract_function(
                target_activations=pos_activations,
                context_activations=neg_activations,
                learning_rate=1e-3,
                num_iterations=500,
            )
        else:
            raise ValueError(f"Unknown extraction method: {extraction_method}")

        logger.info(
            f"Extracted function with {len(result.active_features)} active features"
        )
        return result

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit - clean up interventions."""
        self.remove_all_interventions()
