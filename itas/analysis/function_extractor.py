"""
Improved Function Extractor for SAE-based representation engineering.

This module provides tools for extracting and manipulating specific functions
from SAE feature activations, enabling targeted model behavior modification.
"""

import logging
from typing import Optional, Dict, Any, Tuple, List, Union
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch import Tensor
import numpy as np
from dataclasses import dataclass

from ..core.sae import SAE

logger = logging.getLogger(__name__)


@dataclass
class FunctionExtractionResult:
    """Result from function extraction."""

    function_vector: Tensor
    """Extracted function vector in activation space"""

    feature_weights: Tensor
    """Weights for each SAE feature"""

    active_features: Tensor
    """Indices of active features"""

    extraction_strength: float
    """Strength of the extracted function"""

    metadata: Dict[str, Any]
    """Additional metadata about the extraction"""


class FunctionExtractor(nn.Module):
    """
    Improved function extractor for SAE-based representation engineering.

    Extracts specific behavioral functions from SAE feature activations
    and enables targeted model interventions.
    """

    def __init__(
        self,
        sae: SAE,
        num_features: Optional[int] = None,
        initialization_method: str = "uniform",
        regularization_strength: float = 1e-5,
        device: Optional[Union[str, torch.device]] = None,
    ):
        """
        Initialize function extractor.

        Args:
            sae: Trained SAE model
            num_features: Number of SAE features (auto-detected if None)
            initialization_method: Weight initialization method
            regularization_strength: L2 regularization strength
            device: Device for computation
        """
        super().__init__()

        self.sae = sae
        self.num_features = num_features or sae.d_sae
        self.initialization_method = initialization_method
        self.regularization_strength = regularization_strength

        # Set device and dtype from SAE
        if device is None:
            device = next(sae.parameters()).device
        self.device = device

        # Get dtype from SAE to ensure compatibility
        self.dtype = next(sae.parameters()).dtype

        # Initialize learnable weights for each feature with correct dtype
        self.feature_weights = nn.Parameter(
            torch.zeros(self.num_features, device=device, dtype=self.dtype)
        )

        # Initialize weights
        self._initialize_weights()

        # Activation function (ReLU to ensure non-negative contributions)
        self.activation_fn = nn.ReLU()

        # Move to device with correct dtype
        self.to(device=device, dtype=self.dtype)

    def _initialize_weights(self) -> None:
        """Initialize feature weights."""
        if self.initialization_method == "uniform":
            nn.init.uniform_(self.feature_weights, a=1e-5, b=2e-5)
        elif self.initialization_method == "normal":
            nn.init.normal_(self.feature_weights, mean=0, std=1e-4)
        elif self.initialization_method == "zeros":
            nn.init.zeros_(self.feature_weights)
        elif self.initialization_method == "xavier":
            nn.init.xavier_uniform_(self.feature_weights.unsqueeze(0))
        else:
            raise ValueError(
                f"Unknown initialization method: {self.initialization_method}"
            )

    def forward(
        self,
        feature_activations: Tensor,
        feature_indices: Optional[Tensor] = None,
        max_features_to_remove: Optional[Tensor] = None,
        max_features_to_add: Optional[Tensor] = None,
        return_components: bool = False,
    ) -> Union[Tensor, Tuple[Tensor, ...]]:
        """
        Extract function vector from feature activations.

        Args:
            feature_activations: SAE feature activations
            feature_indices: Indices of active features (for sparse SAEs)
            max_features_to_remove: Maximum activation to remove per feature
            max_features_to_add: Maximum activation to add per feature
            return_components: Whether to return intermediate components

        Returns:
            Function vector in activation space, optionally with components
        """
        # Handle different SAE architectures
        if feature_indices is not None:
            # Sparse SAE (e.g., TopK)
            return self._forward_sparse(
                feature_activations,
                feature_indices,
                max_features_to_remove,
                max_features_to_add,
                return_components,
            )
        else:
            # Dense SAE
            return self._forward_dense(
                feature_activations,
                max_features_to_remove,
                max_features_to_add,
                return_components,
            )

    def _forward_dense(
        self,
        feature_activations: Tensor,
        max_features_to_remove: Optional[Tensor] = None,
        max_features_to_add: Optional[Tensor] = None,
        return_components: bool = False,
    ) -> Union[Tensor, Tuple[Tensor, ...]]:
        """Forward pass for dense SAE."""
        # Apply activation function to weights
        active_weights = self.activation_fn(self.feature_weights)

        # Apply constraints if specified
        if max_features_to_remove is not None:
            active_weights = torch.clamp(active_weights, max=max_features_to_remove)
        elif max_features_to_add is not None:
            active_weights = torch.clamp(active_weights - max_features_to_add, min=0)

        # Compute weighted feature activations
        weighted_activations = feature_activations * active_weights.unsqueeze(0)

        # Decode to activation space
        function_vector = self.sae.decode(weighted_activations)

        if return_components:
            return function_vector, weighted_activations, active_weights
        else:
            return function_vector

    def _forward_sparse(
        self,
        feature_activations: Tensor,
        feature_indices: Tensor,
        max_features_to_remove: Optional[Tensor] = None,
        max_features_to_add: Optional[Tensor] = None,
        return_components: bool = False,
    ) -> Union[Tensor, Tuple[Tensor, ...]]:
        """Forward pass for sparse SAE (TopK)."""
        # Select weights for active features
        selected_weights = self.feature_weights.index_select(
            dim=0, index=feature_indices.flatten()
        ).view_as(feature_activations)

        # Apply activation function
        active_weights = self.activation_fn(selected_weights)

        # Apply constraints if specified
        if max_features_to_remove is not None:
            max_remove_selected = max_features_to_remove.index_select(
                dim=1, index=feature_indices.flatten()
            ).view_as(feature_activations)
            active_weights = torch.clamp(active_weights, max=max_remove_selected)
        elif max_features_to_add is not None:
            max_add_selected = max_features_to_add.index_select(
                dim=1, index=feature_indices.flatten()
            ).view_as(feature_activations)
            active_weights = torch.clamp(active_weights - max_add_selected, min=0)

        # Compute weighted activations
        weighted_activations = active_weights

        # Decode using sparse representation
        if hasattr(self.sae, "decode_sparse"):
            function_vector = self.sae.decode_sparse(
                weighted_activations, feature_indices
            )
        else:
            # Fallback: create full activation vector with correct dtype
            full_activations = torch.zeros(
                feature_activations.shape[0],
                self.num_features,
                device=self.device,
                dtype=self.dtype,
            )
            full_activations.scatter_(
                dim=1, index=feature_indices, src=weighted_activations
            )
            function_vector = self.sae.decode(full_activations)

        if return_components:
            return function_vector, weighted_activations, feature_indices
        else:
            return function_vector

    def extract_function(
        self,
        target_activations: Tensor,
        context_activations: Tensor,
        learning_rate: float = 1e-3,
        num_iterations: int = 1000,
        convergence_threshold: float = 1e-6,
        verbose: bool = True,
    ) -> FunctionExtractionResult:
        """
        Extract function that transforms context to target activations.

        Args:
            target_activations: Target activation patterns
            context_activations: Context activation patterns
            learning_rate: Learning rate for optimization
            num_iterations: Maximum number of optimization iterations
            convergence_threshold: Convergence threshold for early stopping
            verbose: Whether to print progress

        Returns:
            Function extraction result
        """
        logger.info("Starting function extraction")

        # Setup optimizer
        optimizer = torch.optim.Adam([self.feature_weights], lr=learning_rate)

        # Ensure input tensors have correct dtype and device
        target_activations = target_activations.to(device=self.device, dtype=self.dtype)
        context_activations = context_activations.to(
            device=self.device, dtype=self.dtype
        )

        # Extract features from activations
        with torch.no_grad():
            target_features = self.sae.encode(target_activations)
            context_features = self.sae.encode(context_activations)

        prev_loss = float("inf")

        for iteration in range(num_iterations):
            optimizer.zero_grad()

            # Forward pass
            extracted_vector = self.forward(context_features)

            # Compute loss (MSE between extracted and target)
            # Convert to float32 for loss computation if using bfloat16 (CPU limitation)
            pred = context_activations + extracted_vector
            if pred.dtype == torch.bfloat16 and pred.device.type == "cpu":
                pred = pred.float()
                target = target_activations.float()
            else:
                target = target_activations

            mse_loss = F.mse_loss(pred, target)

            # Add regularization
            reg_loss = self.regularization_strength * self.feature_weights.pow(2).mean()

            total_loss = mse_loss + reg_loss

            # Backward pass
            total_loss.backward()
            optimizer.step()

            # Check convergence
            if abs(prev_loss - total_loss.item()) < convergence_threshold:
                if verbose:
                    logger.info(f"Converged at iteration {iteration}")
                break

            prev_loss = total_loss.item()

            if verbose and iteration % 100 == 0:
                logger.info(f"Iteration {iteration}: Loss = {total_loss.item():.6f}")

        # Compute final results
        with torch.no_grad():
            final_vector = self.forward(context_features)
            active_features = (
                (self.activation_fn(self.feature_weights) > 1e-6).nonzero().flatten()
            )
            extraction_strength = self.feature_weights.abs().mean().item()

        result = FunctionExtractionResult(
            function_vector=final_vector,
            feature_weights=self.feature_weights.detach().clone(),
            active_features=active_features,
            extraction_strength=extraction_strength,
            metadata={
                "final_loss": total_loss.item(),
                "iterations": iteration + 1,
                "num_active_features": len(active_features),
                "target_shape": target_activations.shape,
                "context_shape": context_activations.shape,
            },
        )

        logger.info(
            f"Function extraction completed: {len(active_features)} active features"
        )
        return result

    def load_weights(self, weights: Tensor) -> None:
        """
        Load pre-trained feature weights.

        Args:
            weights: Feature weights tensor
        """
        if weights.shape != self.feature_weights.shape:
            raise ValueError(
                f"Weight shape mismatch: expected {self.feature_weights.shape}, "
                f"got {weights.shape}"
            )

        # Ensure weights have correct dtype and device
        weights = weights.to(device=self.device, dtype=self.dtype)

        with torch.no_grad():
            self.feature_weights.copy_(weights)

        logger.info("Loaded feature weights")

    def get_top_features(self, k: int = 10) -> Tuple[Tensor, Tensor]:
        """
        Get top-k most important features.

        Args:
            k: Number of top features to return

        Returns:
            Tuple of (feature_indices, feature_weights)
        """
        with torch.no_grad():
            active_weights = self.activation_fn(self.feature_weights)
            top_values, top_indices = torch.topk(active_weights, k)

        return top_indices, top_values

    def analyze_feature_importance(self) -> Dict[str, Any]:
        """
        Analyze feature importance statistics.

        Returns:
            Dictionary of feature importance statistics
        """
        with torch.no_grad():
            active_weights = self.activation_fn(self.feature_weights)

            stats = {
                "num_active_features": (active_weights > 1e-6).sum().item(),
                "total_features": len(active_weights),
                "mean_weight": active_weights.mean().item(),
                "std_weight": active_weights.std().item(),
                "max_weight": active_weights.max().item(),
                "min_weight": active_weights.min().item(),
                "sparsity": (active_weights == 0).float().mean().item(),
            }

        return stats
