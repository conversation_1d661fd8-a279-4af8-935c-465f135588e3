import os
import torch
import numpy as np
import matplotlib.pyplot as plt
from transformers import AutoTokenizer
import logging

# ITAS imports
import itas
from itas import (
    SAEConfig,
    ModelConfig,
    DatasetConfig,
    TrainingConfig,
    SAETrainer,
    FunctionExtractor,
    RepresentationEngineer,
    SAEEvaluator,
    SAEVisualizer,
    UniversalModelLoader,
    load_model_and_tokenizer,
    create_sae_config,
    DatasetManager,
    validate_config,
    SAE,
)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Check GPU availability
device = "cuda" if torch.cuda.is_available() else "cpu"
print(f"Using device: {device}")
if device == "cuda":
    print(f"GPU: {torch.cuda.get_device_name()}")
    print(f"VRAM: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")

num_gpus = torch.cuda.device_count()

# Model configuration
model_name = "meta-llama/Llama-3.1-8B-Instruct"

# Note: You need HuggingFace access to LLaMA models
# Make sure you're logged in: huggingface-cli login

print(f"Loading {model_name}...")
print(
    f"Using {'multi-GPU' if num_gpus > 1 else 'single-GPU'} setup with {num_gpus} GPU(s)"
)

# Use balanced device mapping for better memory distribution
device_map = "balanced" if num_gpus > 1 else "auto"

# Load model and tokenizer with optimizations
model_loader = UniversalModelLoader(
    ModelConfig(
        model_name=model_name,
        use_flash_attention=True,  # Automatic compatibility detection
        torch_dtype="bfloat16",  # Memory efficient
        device_map=device_map,  # Optimized device placement
        trust_remote_code=False,
        load_for_generation=True,  # Enable text generation capability
    )
)

model, tokenizer = model_loader.load_model_and_tokenizer()

print(f"✓ Model loaded successfully!")
print(f"Model device mapping: {getattr(model, 'hf_device_map', 'Single device')}")
print(f"Model dtype: {next(model.parameters()).dtype}")

# Get detailed model information
model_info = model_loader.get_model_info()
hook_names = model_loader.get_hook_names()

print("📊 Model Information:")
print(f"  Model: {model_info['model_name']}")
print(f"  Architecture: {model_info['architecture']}")
print(f"  Hidden size: {model_info['hidden_size']}")
print(f"  Number of layers: {model_info['num_layers']}")
print(f"  Total parameters: {model_info['total_parameters']:,}")
print(f"  Vocabulary size: {model_info['vocab_size']:,}")

print("\n🔗 Available Hook Points:")
for hook_type, hooks in list(hook_names.items()):
    print(f"  {hook_type}: {len(hooks)} hooks")
    if hooks:
        print(f"    Example: {hooks}")

# Choose a middle layer for SAE training (good balance of complexity and interpretability)
target_layer = model_info["num_layers"] // 2
print(f"\n🎯 Target layer for SAE training: {target_layer}")

# Dataset configuration
dataset_config = DatasetConfig(
    dataset_name="wikitext",
    dataset_kwargs={"name": "wikitext-2-raw-v1"},  # Specify WikiText variant
    dataset_split="train",
    text_column="text",
    max_seq_length=2048,  # LLaMA 3.1 context length
    chunk_size=2048,
    streaming=False,  # Load full dataset for tutorial
    num_proc=4,  # Parallel processing
    trust_remote_code=False,
)

# Get the correct hook names for this model
hook_names = model_loader.get_hook_names()
mlp_hook_pattern = hook_names["mlp_out"]
mlp_hook_name = mlp_hook_pattern.format(layer=target_layer)

print(f"🔗 Available hook patterns: {list(hook_names.keys())}")
print(f"🎯 Using MLP hook: {mlp_hook_name}")

# Create comprehensive SAE configuration
config = SAEConfig(
    # Model configuration
    model=ModelConfig(
        model_name=model_name,
        use_flash_attention=True,
        torch_dtype="bfloat16",
        trust_remote_code=False,
        device_map="auto",
    ),
    # Dataset configuration
    dataset=dataset_config,
    # Training configuration
    training=TrainingConfig(
        total_training_tokens=50_000_000,  # 50M tokens for tutorial
        batch_size=4096 * max(1, num_gpus // 2),  # Scaled for multi-GPU
        learning_rate=3e-4,
        l1_coefficient=1e-3,  # Sparsity regularization
        lr_scheduler="cosine",
        lr_warm_up_steps=1000,
        # Checkpointing and logging
        checkpoint_every_n_tokens=10_000_000,
        save_checkpoint_dir="./checkpoints",
        log_every_n_steps=100,
        eval_every_n_tokens=5_000_000,
        # No W&B for tutorial
        use_wandb=False,
    ),
    # SAE architecture
    architecture="gated",  # Start with gated SAE (typically best performance)
    expansion_factor=32,  # 32x expansion (4096 -> 131,072 features)
    hook_layer=target_layer,  # Middle layer
    hook_name=mlp_hook_name,  # Use correct hook name for this model
    activation_fn="relu",
    normalize_decoder=True,
    # Device and precision
    device="cuda:0",  # Primary device
    dtype="float32",  # Training precision
    seed=42,
)

print("\n⚙️ SAE Configuration:")
print(f"  Architecture: {config.architecture}")
print(f"  Hook layer: {config.hook_layer}")
print(f"  Hook name: {config.hook_name}")
print(f"  Expansion factor: {config.expansion_factor}")
print(f"  Hidden size: {model_info['hidden_size']}")
print(f"  SAE features: {model_info['hidden_size'] * config.expansion_factor:,}")
print(f"  Training tokens: {config.training.total_training_tokens:,}")
print(f"  Batch size: {config.training.batch_size}")

# Validate configuration
try:
    issues = validate_config(config)
    if issues:
        print(f"❌ Configuration issues found: {issues}")
        raise ValueError(f"Invalid configuration: {issues}")
    else:
        print("✓ Configuration is valid!")
except Exception as e:
    print(f"❌ Configuration error: {e}")
    raise

device = "cuda"

# From HuggingFace Hub
layer_idx = 23
# sae = SAE.load(
#     "EleutherAI/sae-llama-3.1-8b-32x", folder_name=f"layers.{layer_idx}.mlp", device=device
# )

# From disk
sae = SAE.load("./sae_models/llama_3_1_8b_layer15_gated_sae.pt", device=device)

for pn, p in sae.named_parameters():
    p.requires_grad = False

# Function extraction: Context vs Knowledge behavior
print("🔍 Starting function extraction...")

# Create function extractor
function_extractor = FunctionExtractor(
    sae=sae,
    initialization_method="uniform",
    regularization_strength=1e-5,
    device=device,
)

# Define examples for different behaviors
context_based_prompts = [
    "Based on the context provided, the answer is clear.",
    "According to the given information, we can conclude that.",
    "The context clearly states that the solution is.",
    "From the provided text, it's evident that.",
    "The passage indicates that the correct answer is.",
]

knowledge_based_prompts = [
    "From my knowledge, I believe the answer is.",
    "Based on what I know, the solution should be.",
    "Generally speaking, this type of problem requires.",
    "In my understanding, the correct approach is.",
    "From general knowledge, we can determine that.",
]


# Get activations for both types of prompts
def get_activations_for_prompts(prompts):
    inputs = tokenizer(prompts, return_tensors="pt", padding=True, truncation=True)
    inputs = {k: v.to(device) for k, v in inputs.items()}

    with torch.no_grad():
        outputs = model(**inputs, output_hidden_states=True)
        activations = outputs.hidden_states[target_layer]
        # Take the last token activation for each sequence
        activations = activations[:, -1, :]  # [batch_size, hidden_size]

    return activations


context_activations = get_activations_for_prompts(context_based_prompts)
knowledge_activations = get_activations_for_prompts(knowledge_based_prompts)

print(f"Context activations shape: {context_activations.shape}")
print(f"Knowledge activations shape: {knowledge_activations.shape}")

# Extract function
print("\n🎯 Extracting behavioral function...")
extraction_result = function_extractor.extract_function(
    target_activations=context_activations,
    context_activations=knowledge_activations,
    learning_rate=1e-3,
    num_iterations=1000,
    verbose=True,
)

print(f"\n✅ Function extraction completed!")
print(f"  Active features: {len(extraction_result.active_features)}")
print(f"  Extraction strength: {extraction_result.extraction_strength:.6f}")
print(f"  Final loss: {extraction_result.metadata.get('final_loss', 'N/A'):.6f}")

# Analyze feature importance
importance_stats = function_extractor.analyze_feature_importance()
print(f"\n📊 Feature Importance Statistics:")
print(f"  Num Active Features: {importance_stats['num_active_features']}")
print(f"  Total Features: {importance_stats['total_features']}")
print(f"  Mean Weight: {importance_stats['mean_weight']:.6f}")
print(f"  Std Weight: {importance_stats['std_weight']:.6f}")
print(f"  Max Weight: {importance_stats['max_weight']:.6f}")
print(f"  Min Weight: {importance_stats['min_weight']:.6f}")
print(f"  Sparsity: {importance_stats['sparsity']:.6f}")

# Get top features
top_features = function_extractor.get_top_features(k=20)
print(f"\n🔝 Top 20 Features: {top_features[:10]}...")

# Create representation engineer
print("🎛️ Setting up representation engineering...")

# Use the already loaded model and tokenizer instead of loading again
engineer = RepresentationEngineer(
    model=model,
    tokenizer=tokenizer,
    sae=sae,
    hook_layer=target_layer,
    hook_name=mlp_hook_name,  # Use the same hook name as SAE training
)

# Create steering vector using our extracted function
print("🧭 Creating steering vector...")
steering_vector = engineer.create_steering_vector(
    positive_examples=context_based_prompts,
    negative_examples=knowledge_based_prompts,
    method="difference",
    # method="pca",
)

print(f"✓ Steering vector created with shape: {steering_vector.shape}")

# Test intervention on some prompts
test_prompts = [
    "What is the capital of France?",
    "How does photosynthesis work?",
    "Explain machine learning in simple terms.",
    "What are the benefits of renewable energy?",
]

print("\n🧪 Testing intervention effects...")

# Apply intervention
intervention_fn = engineer.apply_steering_intervention(
    steering_vector,
    strength=1.5,
    method="add",
    # method="project",
)

generation_kwargs = {
    "max_new_tokens": 50,
    "temperature": 0.7,
    "pad_token_id": tokenizer.eos_token_id,
    "do_sample": True,
}

# Test intervention effectiveness
results = engineer.test_intervention(
    test_prompts,
    intervention_fn,
    generation_kwargs,
)

print("\n📝 Intervention Results:")
print("=" * 80)
for i, (prompt, result) in enumerate(zip(test_prompts, results)):
    print(f"\nPrompt {i+1}: {prompt}")

    # Check if this is an activation comparison result
    if results.is_activation_comparison():
        # For activation comparison results
        print(f"Original: {result['original']}")
        print(f"Modified: {result['modified']}")
        if "activation_diff_norm" in result:
            print(f"Activation difference: {result['activation_diff_norm']:.4f}")
        if "cosine_similarity" in result:
            print(f"Cosine similarity: {result['cosine_similarity']:.4f}")
    else:
        # For text generation results
        if "original" in result and "modified" in result:
            print(f"Original: {result['original'][:100]}...")
            print(f"Modified: {result['modified'][:100]}...")
        elif "original_text" in result and "modified_text" in result:
            print(f"Original: {result['original_text'][:100]}...")
            print(f"Modified: {result['modified_text'][:100]}...")
        else:
            # Fallback - print all available keys
            print(f"Available keys: {list(result.keys())}")
            for key in result:
                if isinstance(result[key], str):
                    print(f"{key}: {result[key][:100]}...")

    print("-" * 40)
